import { CustomEvents } from './util/CustomEvents';
import { GameData } from './GameData/GameData';
import { AutoSaveManager } from './LocalStorage/AutoSaveManager';
import { assetManager } from 'cc';
import { GuideMgr } from './GuideMgr';
import { GameDataStorage } from './LocalStorage/GameDataStorage';
import { DataMigration } from './LocalStorage/DataMigration';
import { ConfigMgr } from './Config/ConfigMgr';
import { EventMessageId } from './util/EventMessageId';
import { SceneEnum } from './SceneManager';
import { game } from 'cc';

export class GameLogic {

    static init() {
        // 初始化自动保存管理器        
        CustomEvents.instance.init()
        AutoSaveManager.getInstance();
        GuideMgr.instance.init()
        GameData.instance.init(assetManager.getBundle("Game"))
        console.log('GameLogic初始化完成，本地存储系统已启动');

    }

    static clear() {
        GameData.instance.clear()
        GuideMgr.instance.clear()
    }

    static initData() {
        // 初始化本地存储系统
        GameDataStorage.init();
        // 执行数据迁移
        DataMigration.performMigration();
        GameDataStorage.initDefaultData();
        // 验证数据完整性
        DataMigration.validateDataIntegrity()

        GameData.instance.loadFromLocalStorage()
        GameData.instance.initCfg()
        GuideMgr.instance.initData()

    }
}