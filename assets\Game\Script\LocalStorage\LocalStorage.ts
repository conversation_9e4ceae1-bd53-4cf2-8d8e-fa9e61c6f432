import { sys } from 'cc';

/**
 * 本地数据存储管理器
 * 支持Cocos Creator 3.8.6的跨平台本地存储
 */
export class LocalStorage {
    private static readonly PREFIX = 'YHRace_';
    private static readonly VERSION_KEY = 'data_version';
    private static readonly CURRENT_VERSION = '1.0.0';

    /**
     * 初始化本地存储
     * 检查数据版本，如果版本不匹配则清理旧数据
     */
    static init(): void {
        const savedVersion = this.getString(this.VERSION_KEY, '');
        if (savedVersion !== this.CURRENT_VERSION) {
            console.log(`数据版本更新: ${savedVersion} -> ${this.CURRENT_VERSION}, 清理旧数据`);
            // this.clearAll();
            this.setString(this.VERSION_KEY, this.CURRENT_VERSION);
        }
    }

    /**
     * 存储字符串数据
     * @param key 键名
     * @param value 字符串值
     */
    static setString(key: string, value: string): void {
        try {
            const fullKey = this.PREFIX + key;
            sys.localStorage.setItem(fullKey, value);
        } catch (error) {
            console.error(`存储字符串失败 [${key}]:`, error);
        }
    }

    /**
     * 获取字符串数据
     * @param key 键名
     * @param defaultValue 默认值
     * @returns 字符串值
     */
    static getString(key: string, defaultValue: string = ''): string {
        try {
            const fullKey = this.PREFIX + key;
            const value = sys.localStorage.getItem(fullKey);
            return value !== null ? value : defaultValue;
        } catch (error) {
            console.error(`获取字符串失败 [${key}]:`, error);
            return defaultValue;
        }
    }

    /**
     * 存储数字数据
     * @param key 键名
     * @param value 数字值
     */
    static setNumber(key: string, value: number): void {
        this.setString(key, value.toString());
    }

    /**
     * 获取数字数据
     * @param key 键名
     * @param defaultValue 默认值
     * @returns 数字值
     */
    static getNumber(key: string, defaultValue: number = 0): number {
        const value = this.getString(key, defaultValue.toString());
        const num = parseFloat(value);
        return isNaN(num) ? defaultValue : num;
    }

    /**
     * 存储布尔值数据
     * @param key 键名
     * @param value 布尔值
     */
    static setBoolean(key: string, value: boolean): void {
        this.setString(key, value ? '1' : '0');
    }

    /**
     * 获取布尔值数据
     * @param key 键名
     * @param defaultValue 默认值
     * @returns 布尔值
     */
    static getBoolean(key: string, defaultValue: boolean = false): boolean {
        const value = this.getString(key, defaultValue ? '1' : '0');
        return value === '1';
    }

    /**
     * 存储JSON对象
     * @param key 键名
     * @param value 对象值
     */
    static setObject<T>(key: string, value: T): void {
        try {
            const jsonString = JSON.stringify(value);
            this.setString(key, jsonString);
        } catch (error) {
            console.error(`存储对象失败 [${key}]:`, error);
        }
    }

    /**
     * 获取JSON对象
     * @param key 键名
     * @param defaultValue 默认值
     * @returns 对象值
     */
    static getObject<T>(key: string, defaultValue: T): T {
        try {
            const jsonString = this.getString(key, '');
            if (jsonString === '') {
                return defaultValue;
            }
            return JSON.parse(jsonString) as T;
        } catch (error) {
            console.error(`获取对象失败 [${key}]:`, error);
            return defaultValue;
        }
    }

    /**
     * 存储数组数据
     * @param key 键名
     * @param value 数组值
     */
    static setArray<T>(key: string, value: T[]): void {
        this.setObject(key, value);
    }

    /**
     * 获取数组数据
     * @param key 键名
     * @param defaultValue 默认值
     * @returns 数组值
     */
    static getArray<T>(key: string, defaultValue: T[] = []): T[] {
        return this.getObject<T[]>(key, defaultValue);
    }

    /**
     * 检查键是否存在
     * @param key 键名
     * @returns 是否存在
     */
    static hasKey(key: string): boolean {
        try {
            const fullKey = this.PREFIX + key;
            return sys.localStorage.getItem(fullKey) !== null;
        } catch (error) {
            console.error(`检查键存在失败 [${key}]:`, error);
            return false;
        }
    }

    /**
     * 删除指定键的数据
     * @param key 键名
     */
    static removeKey(key: string): void {
        try {
            const fullKey = this.PREFIX + key;
            sys.localStorage.removeItem(fullKey);
        } catch (error) {
            console.error(`删除键失败 [${key}]:`, error);
        }
    }

    /**
     * 清空所有本地存储数据
     */
    static clearAll(): void {
        try {
            // 获取所有键
            const keys: string[] = [];
            for (let i = 0; i < sys.localStorage.length; i++) {
                const key = sys.localStorage.key(i);
                if (key && key.startsWith(this.PREFIX)) {
                    keys.push(key);
                }
            }

            // 删除所有匹配的键
            keys.forEach(key => {
                console.log('删除所有匹配的键 ', key);
                sys.localStorage.removeItem(key);
            });

            console.log(`清空本地存储完成，删除了 ${keys.length} 个键`, 'sys.localStorage.length ', sys.localStorage.length);
            console.log('sys.localStorage ', sys.localStorage);
        } catch (error) {
            console.error('清空本地存储失败:', error);
        }
    }

    /**
     * 获取存储使用情况统计
     * @returns 存储统计信息
     */
    static getStorageInfo(): { keyCount: number; totalSize: number; keys: string[] } {
        try {
            const keys: string[] = [];
            let totalSize = 0;

            for (let i = 0; i < sys.localStorage.length; i++) {
                const key = sys.localStorage.key(i);
                if (key && key.startsWith(this.PREFIX)) {
                    keys.push(key.substring(this.PREFIX.length));
                    const value = sys.localStorage.getItem(key);
                    if (value) {
                        totalSize += value.length;
                    }
                }
            }

            return {
                keyCount: keys.length,
                totalSize: totalSize,
                keys: keys
            };
        } catch (error) {
            console.error('获取存储信息失败:', error);
            return { keyCount: 0, totalSize: 0, keys: [] };
        }
    }

    /**
     * 导出所有数据（用于备份）
     * @returns 所有数据的JSON字符串
     */
    static exportData(): string {
        try {
            const data: { [key: string]: string } = {};

            for (let i = 0; i < sys.localStorage.length; i++) {
                const key = sys.localStorage.key(i);
                if (key && key.startsWith(this.PREFIX)) {
                    const value = sys.localStorage.getItem(key);
                    if (value !== null) {
                        data[key.substring(this.PREFIX.length)] = value;
                    }
                }
            }

            return JSON.stringify(data);
        } catch (error) {
            console.error('导出数据失败:', error);
            return '{}';
        }
    }

    /**
     * 导入数据（用于恢复备份）
     * @param dataString JSON格式的数据字符串
     * @returns 是否导入成功
     */
    static importData(dataString: string): boolean {
        try {
            const data = JSON.parse(dataString);

            for (const key in data) {
                if (data.hasOwnProperty(key)) {
                    this.setString(key, data[key]);
                }
            }

            console.log('数据导入成功');
            return true;
        } catch (error) {
            console.error('导入数据失败:', error);
            return false;
        }
    }
}
