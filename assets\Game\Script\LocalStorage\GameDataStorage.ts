import { LocalStorage } from './LocalStorage';
import { ConfigMgr } from '../Config/ConfigMgr';
import { UtilTime } from '../util/UtilTime';
import { ItemId } from '../util/CustomEnum';

/**
 * 游戏数据存储管理器
 * 专门处理游戏相关数据的本地存储
 */
export class GameDataStorage {
    // 存储键名常量
    static readonly KEYS = {
        PLAYER_DATA: 'player_data',
        GAME_SETTINGS: 'game_settings',
        GAME_PROGRESS: 'game_progress',
        BATTLE_HISTORY: 'battle_history',
        GUESS_ROAD_HISTORY: 'guess_road_history',
        EIGHT_ROAD_HISTORY: 'eight_road_history',
        ACHIEVEMENTS: 'achievements',
        BUILD_DATA: "build_data",
        ITEM_DATA: "item_data",
        TASK_DATA: "task_data",
        LAST_LOGIN_TIME: 'last_login_time',
        GUIDE_DATA: 'guide_data',
    };

    /**
     * 初始化游戏数据存储
     */
    static init(): void {
        LocalStorage.init();
    }

    /**
     * 初始化默认数据
     */
    static initDefaultData(): void {
        // 如果是首次运行，设置默认值
        if (!LocalStorage.hasKey(this.KEYS.PLAYER_DATA)) {
            this.setPlayerData(this.getInitPlayerData())
        }

        if (!LocalStorage.hasKey(this.KEYS.GAME_SETTINGS)) {
            this.setGameSettings(this.getInitGameSettings());
        }

        if (!LocalStorage.hasKey(this.KEYS.GAME_PROGRESS)) {
            this.setGameProgress(this.getInitGameProgress());
        }

        if (!LocalStorage.hasKey(this.KEYS.ITEM_DATA)) {
            let lst = [{
                id: ItemId.coupon,
                num: 3
            }]
            this.updateItemDataList(lst);
        }
    }

    static getInitPlayerData(): PlayerData {
        const coinNum = ConfigMgr.getInitCoins()
        const intelligencePoint = ConfigMgr.getIntelligencePoint()
        return {
            playerId: '',
            playerName: '玩家',
            level: 1,
            experience: 0,
            coins: coinNum,
            gems: 50,
            intelligencePoint: intelligencePoint,
            selectedAnimalId: 1,
            ttSideAward: 0,
            promotionTimes: 1,
        };
    }

    static getInitGameSettings(): GameSettings {
        return {
            soundEnabled: true,
            musicEnabled: true,
            soundVolume: 1.0,
            musicVolume: 0.8,
            language: 'zh-CN',
            autoSave: true,
            notifications: true
        };
    }
    static getInitGameProgress(): GameProgress {
        return {
            unlockedMaps: [],
            completedMaps: [],
            mapIndexIds: [], // 比赛入口id
            mapIndexBattleTimes: [],// 对应比赛入口完成次数
            currentMap: 0,
            totalBattles: 0,
            totalWins: 0,
            bestTimes: {}
        }
    }
    // ==================== 玩家数据 ====================

    /**
     * 保存玩家数据
     */
    static setPlayerData(data: PlayerData): void {
        LocalStorage.setObject(this.KEYS.PLAYER_DATA, data);
    }

    /**
     * 获取玩家数据
     */
    static getPlayerData(): PlayerData {
        return LocalStorage.getObject<PlayerData>(this.KEYS.PLAYER_DATA, this.getInitPlayerData());
    }

    // ==================== 游戏设置 ====================

    /**
     * 保存游戏设置
     */
    static setGameSettings(settings: GameSettings): void {
        LocalStorage.setObject(this.KEYS.GAME_SETTINGS, settings);
    }

    /**
     * 获取游戏设置
     */
    static getGameSettings(): GameSettings {
        return LocalStorage.getObject<GameSettings>(this.KEYS.GAME_SETTINGS, this.getInitGameSettings());
    }

    // ==================== 游戏进度 ====================

    /**
     * 保存游戏进度
     */
    static setGameProgress(progress: GameProgress): void {
        LocalStorage.setObject(this.KEYS.GAME_PROGRESS, progress);
    }

    /**
     * 获取游戏进度
     */
    static getGameProgress(): GameProgress {
        return LocalStorage.getObject<GameProgress>(this.KEYS.GAME_PROGRESS, this.getInitGameProgress());
    }

    // ==================== 建筑数据 ====================
    /**
  * 添加建筑数据
  */
    static updateBuildDataList(lst: BulidDataRecord[]): void {
        LocalStorage.setArray(this.KEYS.BUILD_DATA, lst);
    }

    /**
     * 获取建筑数据列表
     */
    static getBuildDataList(): BulidDataRecord[] {
        return LocalStorage.getArray<BulidDataRecord>(this.KEYS.BUILD_DATA, []);
    }

    // ==================== 背包数据 ====================
    static updateItemDataList(lst: ItemDataRecord[]): void {
        LocalStorage.setArray(this.KEYS.ITEM_DATA, lst);
    }

    static getItemDataList(): ItemDataRecord[] {
        return LocalStorage.getArray<ItemDataRecord>(this.KEYS.ITEM_DATA, []);
    }

    // ==================== 引导数据 ====================
    static updateGuideDataList(lst: GuideDataRecord[]): void {
        LocalStorage.setArray(this.KEYS.GUIDE_DATA, lst);
    }

    static getGuideDataList(): GuideDataRecord[] {
        return LocalStorage.getArray<GuideDataRecord>(this.KEYS.GUIDE_DATA, []);
    }

    // ==================== 任务数据 ====================
    static updateTaskDataList(lst: TaskDataRecord[]): void {
        LocalStorage.setArray(this.KEYS.TASK_DATA, lst)
    }

    static getTaskDataList(): TaskDataRecord[] {
        return LocalStorage.getArray<TaskDataRecord>(this.KEYS.TASK_DATA, []);
    }


    // ==================== 战斗历史 ====================

    /**
     * 添加战斗记录
     */
    static addBattleRecord(record: BattleRecord): void {
        const history = this.getBattleHistory();
        history.push(record);

        // 只保留最近100条记录
        if (history.length > 100) {
            history.splice(0, history.length - 100);
        }

        LocalStorage.setArray(this.KEYS.BATTLE_HISTORY, history);
    }

    /**
     * 获取战斗历史
     */
    static getBattleHistory(): BattleRecord[] {
        return LocalStorage.getArray<BattleRecord>(this.KEYS.BATTLE_HISTORY, []);
    }


    // ==================== 猜赛道历史 ====================

    /**
     * 添加猜赛道记录
     * @param index 第几场比赛
     * @param mapIndex 赛道入口
     * @param mapId 地图ID
     * @param roadId 第1名赛道id
     * @param result 结果 我第1 1名 0不是第一名
     */
    static setGuessRoadRecordList(history: GuessRoadRecord[]): void {
        LocalStorage.setArray(this.KEYS.GUESS_ROAD_HISTORY, history);
    }

    /**
     * 获取猜赛道历史
     */
    static getGuessRoadHistory(): GuessRoadRecord[] {
        return LocalStorage.getArray<GuessRoadRecord>(this.KEYS.GUESS_ROAD_HISTORY, []);
    }


    static setEightRoadRecordList(history: GuessRoadRecord[]): void {
        LocalStorage.setArray(this.KEYS.EIGHT_ROAD_HISTORY, history);
    }

    /**
     * 获取猜赛道历史
     */
    static getEightRoadHistory(): GuessRoadRecord[] {
        return LocalStorage.getArray<GuessRoadRecord>(this.KEYS.EIGHT_ROAD_HISTORY, []);
    }


    // ==================== 成就系统 ====================

    /**
     * 解锁成就
     */
    static unlockAchievement(achievementId: string): void {
        const achievements = this.getAchievements();
        if (achievements.indexOf(achievementId) === -1) {
            achievements.push(achievementId);
            LocalStorage.setArray(this.KEYS.ACHIEVEMENTS, achievements);
        }
    }

    /**
     * 获取已解锁的成就
     */
    static getAchievements(): string[] {
        return LocalStorage.getArray<string>(this.KEYS.ACHIEVEMENTS, []);
    }

    /**
     * 检查成就是否已解锁
     */
    static hasAchievement(achievementId: string): boolean {
        return this.getAchievements().indexOf(achievementId) !== -1;
    }

    // ==================== 时间统计 ====================

    /**
     * 更新最后登录时间
     */
    static updateLastLoginTime(): void {
        LocalStorage.setNumber(this.KEYS.LAST_LOGIN_TIME, UtilTime.getTime());
    }

    /**
     * 获取最后登录时间
     */
    static getLastLoginTime(): number {
        return LocalStorage.getNumber(this.KEYS.LAST_LOGIN_TIME, 0);
    }


    // ==================== 数据管理 ====================

    /**
     * 保存所有游戏数据
     */
    static saveAllData(): void {
        console.log('保存所有游戏数据');
        // 这里可以触发所有数据的保存
        // 通常在游戏关键节点调用
    }

    /**
     * 清除所有游戏数据
     */
    static clearAllGameData(): void {
        LocalStorage.clearAll();
        // this.initDefaultData();
        console.log('已清除所有游戏数据');
    }

    /**
     * 获取存储统计信息
     */
    static getStorageStats(): any {
        return LocalStorage.getStorageInfo();
    }
}

// ==================== 数据接口定义 ====================

export interface PlayerData {
    playerId: string;
    playerName: string;
    level: number;
    experience: number;
    coins: number;
    gems: number;
    intelligencePoint: number;  // 情报点
    selectedAnimalId: number;   // 选中的动物ID
    ttSideAward: number // 是否领取抖音侧边栏奖励 0未领取 1领取
    promotionTimes: number //晋级赛次数
}

export interface GameSettings {
    soundEnabled: boolean;
    musicEnabled: boolean;
    soundVolume: number;
    musicVolume: number;
    language: string;
    autoSave: boolean;
    notifications: boolean;
}

export interface GameProgress {
    unlockedMaps: number[];
    completedMaps: number[];
    currentMap: number;
    mapIndexIds: number[],
    mapIndexBattleTimes: number[],
    // 所有比赛次数
    totalBattles: number;
    totalWins: number;

    bestTimes: { [mapId: number]: number };
}

export interface BattleRecord {
    mapId: number;
    animalId: number;
    result: 'win' | 'lose';
    time: number;
    rank: number;
    timestamp: number;
}

export interface GuessRoadRecord {
    index: number // 第几次比赛
    mapIndex: number
    mapId: number
    result: number // 1 第一名 0
    roadId: number // 第一名所在id
    resultList:any[]
}

export interface BulidDataRecord {
    id: number // 建筑id
    cfgId: number // 配置表id
    level: number
    produceTime: number // 开始生产的时间 
    produceCount1: number // 产出总值
    produceCount2: number // 产出总值
    levelTime: number
    levelTimeRecord: number
    inLevelShowRecord:number // 建筑界面内升级表现记录
    outLevelShowRecord:number // 主UI建筑升级表现记录
}

export interface ItemDataRecord {
    id: number
    num: number
}

export interface TaskDataRecord {
    id: number,
    type: number
    award: number,
    data: number
}

export interface GuideDataRecord {
    id: number
}