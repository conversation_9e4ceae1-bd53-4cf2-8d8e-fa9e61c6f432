import { ConfigMgr, ConstantId, MatchMapIndexCfgData, QuestCfgData } from "../Config/ConfigMgr";
import { AnimalData, AttrType } from "./AnimalData";
import { BulidDataRecord, GameDataStorage, GameProgress, GuessRoadRecord, ItemDataRecord, PlayerData, TaskDataRecord } from "../LocalStorage/GameDataStorage";
import { EventMessageId } from "../util/EventMessageId";
import { BuildData } from "./BuildData";
import { UtilTime } from "../util/UtilTime";
import { TaskData, TaskType } from "./TaskData";
import { options, Platforms } from "db://assets/Lanch/options";
import { Inventory, ItemEntry } from "../Inventory/Inventory";
import { LevelData, LevelEffectType } from "./LevelData";
import { CustomEvents } from "../util/CustomEvents";
import { AssetManager } from "cc";


export class GameData {

    static instance: GameData = new GameData()
    private constructor() {

    }
    gameBundle: AssetManager.Bundle;
    curSceneName: string
    userData: PlayerData
    userOldLv: number
    // 动物数据
    animlDataMap: Map<number, AnimalData> = new Map();
    // 猜赛道记录数据
    guessRoadRecordList: GuessRoadRecord[] = []
    // 8赛道记录数据
    eightRoadRecordMap: Map<number, GuessRoadRecord[]> = new Map()

    bulidingDataMap: Map<number, BuildData> = new Map()

    bagData = new Inventory()

    gameProgress: GameProgress = null

    // newPlayerBuffTimes: number = 0
    newPlayerSprintTimes: number = 0
    newPlayerBuffRateList: number[] = []
    newPlayerWinRateRangeList: number[][] = []

    // 任务数据
    taskAwardRecordMap: Map<number, TaskDataRecord> = new Map()

    init(ab: AssetManager.Bundle) {
        this.gameBundle = ab
        CustomEvents.instance.on(EventMessageId.BuildUnlock, this.updateBulidUnlock, this)
    }

    initCfg() {
        this.initConstantData()
        this.initAnimalData()
    }

    clear() {
        CustomEvents.instance.off(EventMessageId.BuildUnlock, this.updateBulidUnlock, this)
        this.guessRoadRecordList = []
        this.eightRoadRecordMap.clear
        this.bulidingDataMap.clear()
        this.animlDataMap.clear()
        this.bagData.clear()
        this.taskAwardRecordMap.clear()
    }


    initConstantData() {
        // this.newPlayerBuffTimes = 0
        // let cfgData = ConfigMgr.getConstant(ConstantId.coefficient_b3)
        // if (cfgData != null) {
        //     this.newPlayerBuffTimes = cfgData.parameter[0]
        // }

    }

    // 新手比赛次数buff
    getNewPlayerBuffRate() {
        const totalBattles = this.gameProgress.totalBattles
        const buffList = ConfigMgr.getBuffdebuffList()
        for (let i = 0; i < buffList.length; i++) {
            const timeRange = buffList[i].times
            if (totalBattles >= timeRange[0] && totalBattles <= timeRange[1]) {
                this.newPlayerWinRateRangeList = buffList[i].win_rate_range
                this.newPlayerBuffRateList = buffList[i].buff_rate
                this.newPlayerSprintTimes = buffList[i].sprint_times_limit
                break
            }
        }

        if (this.newPlayerBuffRateList.length !== this.newPlayerWinRateRangeList.length) {
            console.warn("buffdebuff配置表 数据不匹配")
            return 0
        }

        let winRate = 0
        if (totalBattles > 0) {
            winRate = this.gameProgress.totalWins / totalBattles * 100
        }

        for (let i = 0; i < this.newPlayerWinRateRangeList.length; i++) {
            const range = this.newPlayerWinRateRangeList[i]
            if (range) {
                if (winRate >= range[0] && winRate <= range[1]) {
                    return this.newPlayerBuffRateList[i]
                }
            }
        }
        return 0
    }

    // 狗数据
    initAnimalData() {
        const mapList = ConfigMgr.getAnimalMap()
        for (let [, cfg] of mapList) {
            let data = new AnimalData()
            data.animalId = cfg.ID
            data.mood = 100
            data.physical = 100
            data.statusValue = Math.floor(data.physical * 0.7 + data.mood * 0.3 + 0.0000001)
            data.cfg = cfg
            data.mapAttrTypeData = new Map()
            data.mapAttrTypeData.set(AttrType.speed, data.cfg.speed)
            data.mapAttrTypeData.set(AttrType.obstacle, data.cfg.obstacle)
            data.mapAttrTypeData.set(AttrType.stamina, data.cfg.stamina)
            data.mapAttrTypeData.set(AttrType.sprint, 0)
            this.animlDataMap.set(data.animalId, data)
        }
    }

    // getUserMoney(type: MoneyType) {
    //     if (type == MoneyType.coins) {
    //         return this.userData.coins
    //     }
    //     else if (type == MoneyType.coupon) {
    //         return this.userData.coupon
    //     }
    //     return 0
    // }

    changePromotionTimes(num: number): void {
        this.userData.promotionTimes += num;
        if(this.userData.promotionTimes < 0){
            this.userData.promotionTimes = 0
        }
        if(num < 0){
            TaskData.instance.usePromotionTimes(-num) 
        }
        
        CustomEvents.instance.dispatch(EventMessageId.CheckPlayerData)
    }


    /**
     * 设置金币（带自动保存）
     */
    setCoins(num: number): void {
        this.userData.coins = num;
        CustomEvents.instance.dispatch(EventMessageId.CheckCoins)
        CustomEvents.instance.dispatch(EventMessageId.CheckPlayerData)
    }

    changeCoins(num) {
        this.userData.coins += num
        this.setCoins(this.userData.coins)
        return this.userData.coins
    }

    getCoins(): number {
        return this.userData.coins
    }

    isEnoughCoins(num) {
        return this.userData.coins >= num
    }

    // 物品交互
    addItemId(id: number, num: number) {
        this.bagData.addItemId(id, num)
        CustomEvents.instance.dispatch(EventMessageId.CheckItem)
    }
    removeItemId(id: number, num: number) {
        this.bagData.removeItem(id, num)
        CustomEvents.instance.dispatch(EventMessageId.CheckItem)
    }

    findItem(id: number): ItemEntry {
        return this.bagData.findItem(id)
    }

    // 经验
    getExpProgress() {
        const cfg = ConfigMgr.getLevel(this.userData.level + 1)
        if (cfg) {
            return this.userData.experience / cfg.experience
        }

        return 1
    }

    addExp(exp) {
        this.userData.experience += exp;
        let upLevel = false

        let need = 0
        const oldLv = this.userData.level
        this.userOldLv = oldLv
        while (this.userData.experience >= need) {
            const cfg = ConfigMgr.getLevel(this.userData.level + 1)
            if (cfg) {
                need = cfg.experience
                if (this.userData.experience >= need) {
                    this.userData.level += 1
                    upLevel = true
                    this.userData.experience -= need
                }
                else {
                    break
                }
            }
            else {
                break
            }
        }

        CustomEvents.instance.dispatch(EventMessageId.CheckExp)
        CustomEvents.instance.dispatch(EventMessageId.CheckPlayerData)
        LevelData.instance.addEffect(oldLv, this.userData.level)
        TaskData.instance.updateUserLevel(this.userData.level)

        CustomEvents.instance.dispatch(EventMessageId.CheckUserLevel)
        return upLevel
    }

    getUserLevel() {
        return this.userData.level
    }

    getUserOldLevel() {
        return this.userOldLv
    }

    UpdateUserOldLevel() {
        this.userOldLv = this.userData.level
    }

    /**
     * 设置情报点（带自动保存）
     */
    setIntelligencePoint(points: number): void {
        this.userData.intelligencePoint = points;
        CustomEvents.instance.dispatch(EventMessageId.CheckIntelligencePoint)
    }

    /**
     * 变动情报点（带自动保存）
     */
    changeIntelligencePoint(points: number): number {
        this.userData.intelligencePoint += points;
        this.setIntelligencePoint(this.userData.intelligencePoint)
        return this.userData.intelligencePoint
    }

    getIntelligencePoint(): number {
        return this.userData.intelligencePoint
    }

    // 建筑数据
    createBulid(cfgId) {
        const data = new BuildData(cfgId, 0, cfgId)
        data.produceTime = 0
        data.unlock = LevelData.instance.checkUnlock(LevelEffectType.BuildUnlock, cfgId)
        this.bulidingDataMap.set(cfgId, data)
        CustomEvents.instance.dispatch(EventMessageId.CheckBuildData)
        return data
    }

    getBulidData(id) {
        if (this.bulidingDataMap.has(id)) {
            return this.bulidingDataMap.get(id)
        }

        return this.createBulid(id)
    }

    updateBulidUnlock() {
        for (let [key, data] of this.bulidingDataMap) {
            data.unlock = LevelData.instance.checkUnlock(LevelEffectType.BuildUnlock, key)
        }
    }

    // 记录建筑数据
    updateBulidListRecord() {
        let lst = []
        for (let [key, data] of this.bulidingDataMap) {
            let dataRecord: BulidDataRecord = {
                id: data.id,
                cfgId: data.cfg.ID,
                level: data.level,
                produceTime: data.produceTime,
                produceCount1: data.produceCount1,
                produceCount2: data.produceCount2,
                levelTimeRecord: data.levelTimeRecord,
                levelTime: data.levelTime,
                inLevelShowRecord: data.inLevelShowRecord, // 建筑界面内升级表现记录
                outLevelShowRecord: data.outLevelShowRecord, // 主UI建筑升级表现记录
            }
            lst.push(dataRecord)
        }
        GameDataStorage.updateBuildDataList(lst)
    }

    // 记录道具数据
    updateItemListRecord() {
        let lst = []
        for (let data of this.bagData.getAllItems()) {
            if (data.quantity > 0) {
                let dataRecord: ItemDataRecord = {
                    id: data.item.id,
                    num: data.quantity
                }
                lst.push(dataRecord)
            }
        }
        GameDataStorage.updateItemDataList(lst)
    }

    // 记录任务奖励数据
    setTaskAwardRecord(taskId: number, taskType, progress) {
        let r: TaskDataRecord = {
            id: taskId,
            award: 1,
            data: progress,
            type: taskType
        }

        TaskData.instance.clearTaskItemData(taskType)
        this.taskAwardRecordMap.set(taskId, r)
        CustomEvents.instance.dispatch(EventMessageId.CheckTaskAwardData)
        TaskData.instance.updateTaskList(this.taskAwardRecordMap, this.userData.level)
        CustomEvents.instance.dispatch(EventMessageId.CheckTaskData)
    }

    updateTaskAwardListRecord() {
        let lst = []
        for (let [key, val] of this.taskAwardRecordMap) {
            let dataRecord: TaskDataRecord = {
                id: val.id,
                award: val.award,
                data: val.data,
                type: val.type
            }
            lst.push(dataRecord)
        }
        GameDataStorage.updateTaskDataList(lst)
    }

    updateTaskMatchTimes(taskType: TaskType) {
        const curTask = TaskData.instance.getCurTask()
        if (curTask && curTask.taskType == taskType) {
            TaskData.instance.AddTaskItemData(taskType, 1)
            const progress = TaskData.instance.getTaskItemData(taskType).data1
            const taskId = curTask.cfg.ID
            let r
            if (this.taskAwardRecordMap.has(taskId)) {
                r = this.taskAwardRecordMap.get(curTask.cfg.ID)
                r.data = progress
            } else {
                r = {
                    id: taskId,
                    award: 0,
                    data: progress,
                    type: taskType
                }
            }
            this.taskAwardRecordMap.set(taskId, r)
            CustomEvents.instance.dispatch(EventMessageId.CheckTaskAwardData)
        }
    }

    /**
    * 添加猜赛道记录
    * @param mapIndex 赛道入口
    * @param mapId 地图ID
    * @param roadId 第1名赛道id
    * @param result 结果 我第1 1名 0不是第一名
    * @param lst 所有结果
    */
    addGuessRoadRecord(mapIndex: number, mapId: number, result: number, roadId: number, lst: any[]): void {
        if (this.guessRoadRecordList.length == 0) {
            const history = GameDataStorage.getGuessRoadHistory()
            this.guessRoadRecordList = history
        }
        // 场次计算
        let length = this.guessRoadRecordList.length
        let index = 1
        if (length > 0) {
            index = this.guessRoadRecordList[length - 1].index + 1
        }

        let record: GuessRoadRecord = {
            index: index,
            mapIndex: mapIndex,
            mapId: mapId,
            roadId: roadId,
            result: result,
            resultList: lst
        }

        this.guessRoadRecordList.push(record);
        length = this.guessRoadRecordList.length
        // 只保留最近20条记录
        if (length > 20) {
            this.guessRoadRecordList.splice(0, length - 20);
        }
        GameDataStorage.setGuessRoadRecordList(this.guessRoadRecordList)

        this.updateTaskMatchTimes(TaskType.GuessRoad)

    }

    initEightRoadRecod() {
        if (this.eightRoadRecordMap.size == 0) {
            const history = GameDataStorage.getEightRoadHistory()
            for (let i = 0; i < history.length; i++) {
                const d = history[i]
                if (this.eightRoadRecordMap.has(d.mapIndex)) {
                    this.eightRoadRecordMap.get(d.mapIndex).push(d)
                }
                else {
                    let lst = []
                    lst.push(d)
                    this.eightRoadRecordMap.set(d.mapIndex, lst)
                }
            }
        }
    }

    addEightRoadRecord(mapIndex: number, mapId: number, result: number, roadId: number, lst: any[]): void {
        this.initEightRoadRecod()
        let lstData
        if (this.eightRoadRecordMap.has(mapIndex)) {
            lstData = this.eightRoadRecordMap.get(mapIndex)
        } else {
            lstData = []
            this.eightRoadRecordMap.set(mapIndex, lstData)
        }

        let length = lstData.length
        let index = 1
        if (length > 0) {
            index = lstData[length - 1].index + 1
        }

        // 场次计算
        let record: GuessRoadRecord = {
            index: index,
            mapIndex: mapIndex,
            mapId: mapId,
            roadId: roadId,
            result: result,
            resultList: lst,
        }

        lstData.push(record);

        length = lstData.length
        // 只保留最近20条记录
        if (length > 20) {
            lstData.splice(0, length - 20);
        }

        let lstAll = []
        for (let [id, lst] of this.eightRoadRecordMap) {
            for (let i = 0; i < lst.length; i++) {
                lstAll.push(lst[i])
            }
        }

        GameDataStorage.setEightRoadRecordList(lstAll)
        this.updateTaskMatchTimes(TaskType.EightRoad)
    }

    updateGameProgress(mapIndex: number, mapId: number, result: number) {
        // 更新比赛总场次和总胜利次数
        this.gameProgress.totalBattles++;
        if (result === 1) {
            this.gameProgress.totalWins++;
        }
        let index = this.gameProgress.mapIndexIds.indexOf(mapIndex)
        let times = 1
        if (index == -1) {
            this.gameProgress.mapIndexIds.push(mapIndex)
            this.gameProgress.mapIndexBattleTimes.push(times)
        } else {
            times = this.gameProgress.mapIndexBattleTimes[index] + 1
            this.gameProgress.mapIndexBattleTimes[index] = times
        }

        console.log("mapIndex", mapIndex, ',index ', index, 'battleTimes ', times, '总胜利次数', this.gameProgress.totalWins, '总比赛次数', this.gameProgress.totalBattles)
        // 更新最佳时间
        // if (result === 'win' && (!progress.bestTimes[mapId] || time < progress.bestTimes[mapId])) {
        //     progress.bestTimes[mapId] = time;
        // }

        CustomEvents.instance.dispatch(EventMessageId.CheckGameProgress)
    }


    // ==================== 本地存储相关方法 ====================

    /**
     * 从本地存储加载数据
     */
    loadFromLocalStorage(): void {
        try {
            // 加载玩家数据
            const playerData = GameDataStorage.getPlayerData();

            this.userData = playerData
            if (this.userData.promotionTimes == null)
                this.userData.promotionTimes = 1
            const last_login_time = GameDataStorage.getLastLoginTime()
            if (!UtilTime.isSameDay(last_login_time)) {
                this.userData.ttSideAward = 0

                const c4 = ConfigMgr.getConstant(ConstantId.coefficient_c4)
                if(c4){
                    this.userData.promotionTimes = c4.parameter[0]
                }
                // if (options.platform == Platforms.MINI_BYTE_DANCE) { }
                console.log('跨天重置')
            }

            // 初始化 等级解锁数据
            LevelData.instance.initEffect(this.userData.level)
            TaskData.instance.updateUserLevel(this.userData.level)

            // 加载建筑数据
            const bulidList = GameDataStorage.getBuildDataList()
            if (bulidList) {
                for (let i = 0; i < bulidList.length; i++) {
                    const d = bulidList[i]
                    const data = new BuildData(d.id, d.level, d.cfgId)
                    data.produceTime = d.produceTime
                    data.produceCount1 = d.produceCount1
                    data.produceCount2 = d.produceCount2
                    data.levelTime = d.levelTime
                    data.levelTimeRecord = d.levelTimeRecord
                    data.inLevelShowRecord = d.inLevelShowRecord == null ? 0 : d.inLevelShowRecord
                    data.outLevelShowRecord = d.outLevelShowRecord == null ? 0 : d.outLevelShowRecord
                    data.unlock = LevelData.instance.checkUnlock(LevelEffectType.BuildUnlock, d.id)
                    this.bulidingDataMap.set(d.id, data)
                    TaskData.instance.updateBulidLevel(d.id, d.level)
                }
            }
            // this.userData.experience = 1000
            // 游戏进度
            this.gameProgress = GameDataStorage.getGameProgress()

            // 物品数据
            const itemList = GameDataStorage.getItemDataList()
            for (let i = 0; i < itemList.length; i++) {
                const d = itemList[i]
                this.bagData.addItemId(d.id, d.num)
            }

            // 任务领奖记录
            const taskAwardLit = GameDataStorage.getTaskDataList()
            console.log("从本地存储加载游戏数据完成 taskAwardLit", taskAwardLit)
            for (let i = 0; i < taskAwardLit.length; i++) {
                this.taskAwardRecordMap.set(taskAwardLit[i].id, taskAwardLit[i])
                // 统计赛道任务数据
                TaskData.instance.updateAllMatchMapTimes(taskAwardLit[i])
            }

            // 获取任务列表
            TaskData.instance.updateTaskList(this.taskAwardRecordMap, this.userData.level)
            // 更新登录时间
            GameDataStorage.updateLastLoginTime()
            // console.log("从本地存储加载游戏数据完成 playerData", playerData)
            // console.log('从本地存储加载游戏数据完成 gameProgress', this.gameProgress);
            // console.log('从本地存储加载游戏数据完成 this.bulidingDataMap', this.bulidingDataMap);
            // console.log('从本地存储加载游戏数据完成 bulidList ', bulidList);
        } catch (error) {
            console.error('从本地存储加载数据失败:', error);
        }
    }

    /**
     * 保存数据到本地存储
     */
    saveToLocalStorage(): void {
        try {
            // 保存情玩家
            GameDataStorage.setPlayerData(this.userData);

            // 标记数据已变更
            CustomEvents.instance.dispatch(EventMessageId.CheckLockData)

            console.log('保存游戏数据到本地存储完成');
        } catch (error) {
            console.error('保存数据到本地存储失败:', error);
        }
    }

}


